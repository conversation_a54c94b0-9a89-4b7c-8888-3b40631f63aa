#include "stdafx.h"
#include "AtsMidParser.h"
#include "MidGateway.h"

int main(int argc, char* argv[])
{
	ats::init_application(argc, argv);
	std::string fileName = ats::get_last_midfile();

	if (std::filesystem::exists(fileName)) {
		ats::reset_elapsed_time();

		AtsMidParser axParser;
		axParser.win_readNetis_Bymmap(fileName);

		ats::print_elapsed_time();
	}
	else {
		std::cerr << "文件不存在，请检查路径。" << std::endl;
		return 1;
	}

	// 使用标准输入等待用户按键
	std::cout << "按任意键继续..." << std::endl;
	std::cin.ignore((std::numeric_limits<std::streamsize>::max)(), '\n');
	return 0;
}